'use client';

import React, { useState } from 'react';
import { CheckCircle, Download } from 'lucide-react';

import { Button } from '@/components/ui/Button';
import { Tab } from '@headlessui/react';
import V2DataGrid from '@/components/ui/V2DataGrid';

interface DistributionProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onDistributionComplete?: (data: any) => void;
  pivotData: any;
}

const Distribution: React.FC<DistributionProps> = ({
  onNext,
  pivotData
}) => {
  const [selectedTab, setSelectedTab] = useState(0); // 0 = SMC, 1 = Vessel
  const [selectedView, setSelectedView] = useState(0); // 0 = Summary, 1 = Splits, 2 = Distribution

  // Extract data from pivotData
  const distributionData = pivotData?.result?.database_records || {};
  const summaryData = distributionData.pivot_summaries || {};
  const splitsData = distributionData.pivot_splits || {};
  const distributionsData = distributionData.pivot_distributions || {};

  // SMC Headers for Summary
  const smcSummaryHeaders = [
    { key: 'Entity Name', name: 'Entity Name', width: 150, filterType: '' },
    { key: 'SMC Name', name: 'SMC Name', width: 150, filterType: '' },
    { key: 'Total BF Amount', name: 'Total BF Amount', filterType: '' },
  ];

  // Vessel Headers for Summary
  const vesselSummaryHeaders = [
    { key: 'Entity Name', name: 'Entity Name', width: 150, filterType: '' },
    { key: 'Vessel Name', name: 'Vessel Name', filterType: '' },
    { key: 'SMC Name', name: 'SMC Name', width: 150, filterType: '' },
    { key: 'Total BF Amount', name: 'Total BF Amount', width: 150, filterType: '' },
  ];

  // SMC Headers for Splits
  const smcSplitsHeaders = [
    { key: 'Entity Name', name: 'Entity Name', width: 150, filterType: '' },
    { key: 'SMC Name', name: 'SMC Name', width: 150, filterType: '' },
    { key: 'Entity Amount', name: 'Entity Amount', width: 150, filterType: '' },
    { key: 'Seachef Amount', name: 'Seachef Amount', width: 150, filterType: '' },
  ];

  // Vessel Headers for Splits
  const vesselSplitsHeaders = [
    { key: 'Entity Name', name: 'Entity Name', width: 150, filterType: '' },
    { key: 'Vessel Name', name: 'Vessel Name', width: 150, filterType: '' },
    { key: 'SMC Name', name: 'SMC Name', width: 150, filterType: '' },
    { key: 'Entity Amount', name: 'Entity Amount', width: 150, filterType: '' },
    { key: 'Seachef Amount', name: 'Seachef Amount', width: 150, filterType: '' },
  ];

  // SMC Headers for Distributions
  const smcDistributionHeaders = [
    { key: 'Entity Name', name: 'Entity Name', width: 150, filterType: '' },
    { key: 'SMC Name', name: 'SMC Name', width: 150, filterType: '' },
    { key: 'Base BF Amount', name: 'Base BF Amount', width: 150, filterType: '' },
    { key: 'Total BF Including 50%', name: 'Total BF Including 50%', width: 180, filterType: '' },
    { key: 'BF Percentage', name: 'BF Percentage', width: 150, filterType: '' },
    { key: 'Final Distribution Amount', name: 'Final Distribution Amount', width: 200, filterType: '' },
  ];

  // Vessel Headers for Distributions
  const vesselDistributionHeaders = [
    { key: 'Entity Name', name: 'Entity Name', width: 150, filterType: '' },
    { key: 'Vessel Name', name: 'Vessel Name', width: 150, filterType: '' },
    { key: 'SMC Name', name: 'SMC Name', width: 150, filterType: '' },
    { key: 'Base BF Amount', name: 'Base BF Amount', width: 150, filterType: '' },
    { key: 'Total BF Including 50%', name: 'Total BF Including 50%', width: 180, filterType: '' },
    { key: 'BF Percentage', name: 'BF Percentage', width: 150, filterType: '' },
    { key: 'Final Distribution Amount', name: 'Final Distribution Amount', width: 200, filterType: '' },
  ];

  // Map SMC Summary data
  const smcSummaryData = (summaryData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Total BF Amount': item.total_bf_amount?.toFixed(2),
  }));

  // Map Vessel Summary data
  const vesselSummaryData = (summaryData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Total BF Amount': item.total_bf_amount?.toFixed(2),
  }));

  // Map SMC Splits data
  const smcSplitsDataMapped = (splitsData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Entity Amount': item.entity_amount?.toFixed(2),
    'Seachef Amount': item.seachef_amount?.toFixed(2),
  }));

  // Map Vessel Splits data
  const vesselSplitsDataMapped = (splitsData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Entity Amount': item.entity_amount?.toFixed(2),
    'Seachef Amount': item.seachef_amount?.toFixed(2),
  }));

  // Map SMC Distribution data
  const smcDistributionData = (distributionsData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Base BF Amount': item.base_bf_amount?.toFixed(2),
    'Total BF Including 50%': item.total_bf_including_50_percent?.toFixed(2),
    'BF Percentage': item.bf_percentage?.toFixed(4) + '%',
    'Final Distribution Amount': item.final_distribution_amount?.toFixed(2),
  }));

  // Map Vessel Distribution data
  const vesselDistributionData = (distributionsData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Base BF Amount': item.base_bf_amount?.toFixed(2),
    'Total BF Including 50%': item.total_bf_including_50_percent?.toFixed(2),
    'BF Percentage': item.bf_percentage?.toFixed(4) + '%',
    'Final Distribution Amount': item.final_distribution_amount?.toFixed(2),
  }));

  // Summary data
  const smcRecords = pivotData?.result?.smc_summary_records || 0;
  const vesselRecords = pivotData?.result?.vessel_summary_records || 0;

  // Main tab configuration (SMC and Vessel)
  const mainTabs = ['SMC', 'Vessel'];

  // View buttons configuration
  const viewButtons = ['Summary', 'Splits', 'Distribution'];

  // Function to get current data and headers based on selected tab and view
  const getCurrentDataAndHeaders = () => {
    const isSmcTab = selectedTab === 0;

    switch (selectedView) {
      case 0: // Summary
        return {
          data: isSmcTab ? smcSummaryData : vesselSummaryData,
          headers: isSmcTab ? smcSummaryHeaders : vesselSummaryHeaders,
          title: isSmcTab ? 'SMC Summary' : 'Vessel Summary'
        };
      case 1: // Splits
        return {
          data: isSmcTab ? smcSplitsDataMapped : vesselSplitsDataMapped,
          headers: isSmcTab ? smcSplitsHeaders : vesselSplitsHeaders,
          title: isSmcTab ? 'SMC Splits' : 'Vessel Splits'
        };
      case 2: // Distribution
        return {
          data: isSmcTab ? smcDistributionData : vesselDistributionData,
          headers: isSmcTab ? smcDistributionHeaders : vesselDistributionHeaders,
          title: isSmcTab ? 'SMC Distribution' : 'Vessel Distribution'
        };
      default:
        return {
          data: [],
          headers: [],
          title: ''
        };
    }
  };

  const currentDataConfig = getCurrentDataAndHeaders();

  return (
    <div className="flex flex-col h-full">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0 mb-3">
        {/* Custom Tabs with Download Button */}
        <div className="bg-white rounded-lg">
          <Tab.Group selectedIndex={selectedTab} onChange={setSelectedTab}>
            <Tab.List className="border-b-[1px] border-lightgray-100 flex items-center justify-between">
              <div className="flex">
                {mainTabs.map((tab) => (
                  <Tab
                    key={`tab-title-${tab}`}
                    className={({ selected }) =>
                      `px-4 py-2 text-sm font-medium focus:outline-none ${
                        selected
                          ? 'text-gray-900 border-b-2 border-gray-900'
                          : 'text-gray-500 hover:text-gray-700'
                      }`
                    }
                  >
                    {tab}
                  </Tab>
                ))}
              </div>
              <div className="pr-4">
                <Button
                  onClick={() => {
                    const reportUrl = pivotData?.result?.comprehensive_report_url;
                    if (reportUrl) {
                      window.open(reportUrl, '_blank');
                    }
                  }}
                  className="flex h-[28px] items-center px-3 text-xs"
                  intent="secondary"
                  disabled={!pivotData?.result?.comprehensive_report_url}
                >
                  <Download className="w-3 h-3 mr-1" />
                  Download Report
                </Button>
              </div>
            </Tab.List>
            <Tab.Panels>
              {mainTabs.map((tab, index) => (
                <Tab.Panel key={`tab-panel-${tab}`}>
                  {/* View Selection Buttons */}
                  <div className="p-4 border-b border-gray-200">
                    <div className="flex space-x-2">
                      {viewButtons.map((view, viewIndex) => (
                        <Button
                          key={view}
                          onClick={() => setSelectedView(viewIndex)}
                          className={`h-[32px] px-4 text-xs font-medium ${
                            selectedView === viewIndex
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {view}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Data Grid */}
                  <div className="p-4">
                    <h3 className="text-lg font-semibold mb-3 text-gray-800">{currentDataConfig.title}</h3>
                    <V2DataGrid
                      data={currentDataConfig.data}
                      headerList={currentDataConfig.headers}
                    />
                  </div>
                </Tab.Panel>
              ))}
            </Tab.Panels>
          </Tab.Group>
        </div>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="size-4 text-green-500" />
            <span className="text-xs font-medium text-green-600">Distribution complete</span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-600">{smcRecords} SMC records • {vesselRecords} vessel records processed</span>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button
              onClick={() => onNext?.()}
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
            >
              Continue to Export →
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Distribution;